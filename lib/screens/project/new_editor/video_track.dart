import 'dart:async';
import 'dart:io';

import 'package:ai_video_creator_editor/screens/project/models/video_track_model.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/custom_trim_slider.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
// TrackOptions and TrackType imports removed - handled by parent widget
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';

// Trim overlay painter for visual feedback during dragging
class TrimOverlayPainter extends CustomPainter {
  final double visualTrimStart;
  final double visualTrimEnd;
  final double currentTrimStart;
  final double currentTrimEnd;
  final double originalDuration;
  final bool isDragging;
  final int thumbnailCount;

  TrimOverlayPainter({
    required this.visualTrimStart,
    required this.visualTrimEnd,
    required this.currentTrimStart,
    required this.currentTrimEnd,
    required this.originalDuration,
    required this.isDragging,
    required this.thumbnailCount,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (!isDragging || thumbnailCount <= 0) return;

    final maskPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.6)
      ..style = PaintingStyle.fill;

    // Calculate current state duration (what's currently shown)
    final currentDuration = currentTrimEnd - currentTrimStart;
    if (currentDuration <= 0) return;

    // Map visual trim values relative to current state
    // Visual trim values are in original timeline coordinates
    // We need to convert them to current timeline coordinates

    // Calculate what portion of current thumbnails should be masked
    final visualStart = visualTrimStart;
    final visualEnd = visualTrimEnd;

    // Convert to ratios within current range (0.0 to 1.0)
    final startRatio =
        ((visualStart - currentTrimStart) / currentDuration).clamp(0.0, 1.0);
    final endRatio =
        ((visualEnd - currentTrimStart) / currentDuration).clamp(0.0, 1.0);

    print("=== Masking calculation ===");
    print(
        "Current trim range: $currentTrimStart - $currentTrimEnd (${currentDuration}s)");
    print("Visual trim range: $visualStart - $visualEnd");
    print("Mask ratios: $startRatio - $endRatio");

    // Left mask (area before visual trim start)
    if (startRatio > 0.0) {
      final leftMaskWidth = size.width * startRatio;
      canvas.drawRect(
        Rect.fromLTWH(0, 0, leftMaskWidth, size.height),
        maskPaint,
      );
      print("Drawing left mask: width = $leftMaskWidth");
    }

    // Right mask (area after visual trim end)
    if (endRatio < 1.0) {
      final rightMaskStart = size.width * endRatio;
      final rightMaskWidth = size.width - rightMaskStart;
      canvas.drawRect(
        Rect.fromLTWH(rightMaskStart, 0, rightMaskWidth, size.height),
        maskPaint,
      );
      print(
          "Drawing right mask: start = $rightMaskStart, width = $rightMaskWidth");
    }
  }

  @override
  bool shouldRepaint(TrimOverlayPainter oldDelegate) {
    return oldDelegate.visualTrimStart != visualTrimStart ||
        oldDelegate.visualTrimEnd != visualTrimEnd ||
        oldDelegate.currentTrimStart != currentTrimStart ||
        oldDelegate.currentTrimEnd != currentTrimEnd ||
        oldDelegate.isDragging != isDragging ||
        oldDelegate.thumbnailCount != thumbnailCount;
  }
}

class VideoTrack extends StatefulWidget {
  const VideoTrack({
    super.key,
    required this.videoTrack,
    required this.index,
    required this.isSelected,
    required this.selectedTrackBorderColor,
    this.onLongPress,
  });

  final VideoTrackModel videoTrack;
  final int index;
  final bool isSelected;
  final Color selectedTrackBorderColor;
  final Function(Offset)? onLongPress;

  @override
  State<VideoTrack> createState() => _VideoTrackState();
}

class _VideoTrackState extends State<VideoTrack>
    with AutomaticKeepAliveClientMixin {
  final ValueNotifier<List<File>> _thumbnailNotifier =
      ValueNotifier<List<File>>([]);
  bool _isGeneratingThumbnails = false;

  // Trim functionality - Data layer (actual trim values)
  TrimBoundaries _boundary = TrimBoundaries.none;
  double _trimStart = 0.0;
  double _trimEnd = 0.0;
  Timer? _debounceTimer;

  // Visual layer (for immediate feedback)
  double _visualTrimStart = 0.0;
  double _visualTrimEnd = 0.0;
  bool _isDragging = false;

  // Overlay-related variables removed - handled by parent GestureDetector

  @override
  void initState() {
    super.initState();
    _trimStart = widget.videoTrack.videoTrimStart;
    _trimEnd = widget.videoTrack.videoTrimEnd;
    _visualTrimStart = _trimStart;
    _visualTrimEnd = _trimEnd;
    setState(() {
      _isGeneratingThumbnails = true;
    });
    _generateThumbnailAtTime(widget.videoTrack.processedFile.path);
  }

  @override
  void didUpdateWidget(VideoTrack oldWidget) {
    super.didUpdateWidget(oldWidget);

    print("=== VideoTrack didUpdateWidget called ===");
    print("Old file: ${oldWidget.videoTrack.processedFile.path}");
    print("New file: ${widget.videoTrack.processedFile.path}");
    print("Old trim start: ${oldWidget.videoTrack.videoTrimStart}");
    print("New trim start: ${widget.videoTrack.videoTrimStart}");
    print("Old trim end: ${oldWidget.videoTrack.videoTrimEnd}");
    print("New trim end: ${widget.videoTrack.videoTrimEnd}");

    // Update data layer trim values when provider updates
    if (oldWidget.videoTrack.videoTrimStart !=
            widget.videoTrack.videoTrimStart ||
        oldWidget.videoTrack.videoTrimEnd != widget.videoTrack.videoTrimEnd) {
      _trimStart = widget.videoTrack.videoTrimStart;
      _trimEnd = widget.videoTrack.videoTrimEnd;
      // If not dragging, sync visual values
      if (!_isDragging) {
        _visualTrimStart = _trimStart;
        _visualTrimEnd = _trimEnd;
      }
    }

    // Only regenerate thumbnails if the file itself changed
    if (oldWidget.videoTrack.processedFile.path !=
        widget.videoTrack.processedFile.path) {
      print("File changed, regenerating thumbnails...");
      setState(() {
        _isGeneratingThumbnails = true;
      });
      _generateThumbnailAtTime(widget.videoTrack.processedFile.path);
    } else {
      print("No file changes, keeping existing thumbnails");
    }
  }

  /// Generate stable identifier for thumbnails based on file path and creation time
  String _generateStableThumbnailId(String filePath) {
    // Use file path hash combined with original file stats for stability
    // This ensures thumbnails are only regenerated when the actual file changes
    final fileHash = filePath.hashCode;
    final trackId = widget.videoTrack.id;
    return 'fullvideo_${trackId}_${fileHash}_${widget.videoTrack.originalDuration.toInt()}';
  }

  Future<void> _generateThumbnailAtTime(String filePath) async {
    try {
      final Directory tempDir = await getTemporaryDirectory();

      print(
          "=== Generating full-video thumbnails for track ${widget.videoTrack.id} ===");
      print("File path: $filePath");
      print("Original duration: ${widget.videoTrack.originalDuration}");
      print(
          "Current trim: ${widget.videoTrack.videoTrimStart} - ${widget.videoTrack.videoTrimEnd}");

      // Use stable identifier instead of lastModified timestamp
      final stableId = _generateStableThumbnailId(filePath);

      // Check if thumbnails already exist for this stable ID
      final existingFiles = await tempDir.list().where((entity) {
        return entity.path.contains(stableId);
      }).toList();

      if (existingFiles.isNotEmpty) {
        print("Using existing full-video thumbnails for stable ID: $stableId");
        final thumbnailFiles = existingFiles.map((e) => File(e.path)).toList()
          ..sort((a, b) {
            int extractNumber(String path) {
              String fileName = p.basename(path);
              final match = RegExp(r'frame_(\d+)\.jpg').firstMatch(fileName);
              return int.tryParse(match?.group(1) ?? '0') ?? 0;
            }

            return extractNumber(a.path).compareTo(extractNumber(b.path));
          });

        if (mounted) {
          _thumbnailNotifier.value = thumbnailFiles;
          setState(() {
            _isGeneratingThumbnails = false;
          });
        }
        return;
      }

      // Clear old thumbnails for this track (different naming patterns)
      await _clearOldThumbnails(tempDir);

      final String outputPattern = '${tempDir.path}/${stableId}_frame_%d.jpg';

      // Generate thumbnails from FULL original video (not trimmed range)
      // This provides a complete thumbnail library that we can mask visually
      final originalDuration = widget.videoTrack.originalDuration;

      final command =
          '-i "$filePath" -vf "fps=1,scale=160:90" -t $originalDuration -q:v 2 "$outputPattern"';

      print("Generating full-video thumbnails with command: $command");

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      print("Full-video thumbnail generation return code: $returnCode");

      if (!ReturnCode.isSuccess(returnCode)) {
        print("Failed to generate thumbnails for ${widget.videoTrack.id}");
        if (mounted) _thumbnailNotifier.value = [];
        return;
      }

      final List<File> files = (await tempDir.list().where((entity) {
        return entity.path.contains(stableId);
      }).map((entity) {
        return File(entity.path);
      }).toList())
        ..sort((a, b) {
          int extractNumber(String path) {
            String fileName = p.basename(path);
            final match = RegExp(r'frame_(\d+)\.jpg').firstMatch(fileName);
            return int.tryParse(match?.group(1) ?? '0') ?? 0;
          }

          return extractNumber(a.path).compareTo(extractNumber(b.path));
        });

      print(
          "Generated ${files.length} thumbnails for track ${widget.videoTrack.id}");
      if (mounted) {
        _thumbnailNotifier.value = files;
        setState(() {
          _isGeneratingThumbnails = false;
        });
      }
    } catch (e) {
      print("Error generating thumbnails: $e");
      if (mounted) {
        _thumbnailNotifier.value = [];
        setState(() {
          _isGeneratingThumbnails = false;
        });
      }
    }
  }

  Future<void> _clearOldThumbnails(Directory tempDir) async {
    try {
      // Clear old thumbnails with different naming patterns (legacy and current)
      final trackId = widget.videoTrack.id;
      final currentStableId =
          _generateStableThumbnailId(widget.videoTrack.processedFile.path);
      final oldFiles = await tempDir.list().where((entity) {
        final path = entity.path;
        // Clear old timestamp-based thumbnails and any other patterns for this track
        return (path.contains("video_track${widget.index}_${trackId}_") ||
                path.contains("fullvideo_${trackId}_")) &&
            path.contains("_frame_") &&
            !path.contains(
                currentStableId); // Don't clear current stable thumbnails
      }).toList();

      for (final file in oldFiles) {
        if (file is File) {
          await file.delete();
        }
      }
      print(
          "Cleared ${oldFiles.length} old thumbnails for track ${widget.videoTrack.id}");
    } catch (e) {
      print("Error clearing old thumbnails: $e");
    }
  }

  void _onPanStart() {
    setState(() {
      _isDragging = true;
    });
  }

  void _onPanEnd() {
    // Update data layer with final visual values
    _trimStart = _visualTrimStart;
    _trimEnd = _visualTrimEnd;

    setState(() {
      _isDragging = false;
    });

    // Cancel any existing timer
    _debounceTimer?.cancel();

    // Update provider after longer delay for better UX
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      context
          .read<VideoEditorProvider>()
          .updateVideoTrack(widget.index, _trimStart, _trimEnd);
    });
  }

  void _onPanUpdate(DragUpdateDetails details, double trackWidth) {
    if (_boundary == TrimBoundaries.none || !widget.isSelected) return;

    final originalDuration = widget.videoTrack.originalDuration;
    if (originalDuration <= 0) return;

    // Calculate delta based on drag movement
    final delta =
        details.delta.dx / (MediaQuery.of(context).size.width / 8) * 1.0;
    const double minTrimSize = 0.5;

    // Set boundaries within the original video duration
    final double lowerLimit = 0;
    final double upperLimit = originalDuration;

    // Update visual values immediately for real-time feedback
    setState(() {
      switch (_boundary) {
        case TrimBoundaries.start:
          _visualTrimStart = (_visualTrimStart + delta)
              .clamp(lowerLimit, _visualTrimEnd - minTrimSize);
          break;

        case TrimBoundaries.end:
          _visualTrimEnd = (_visualTrimEnd + delta)
              .clamp(_visualTrimStart + minTrimSize, upperLimit);
          break;

        case TrimBoundaries.inside:
          final length = _visualTrimEnd - _visualTrimStart;
          var newStart =
              (_visualTrimStart + delta).clamp(lowerLimit, upperLimit - length);
          _visualTrimStart = newStart;
          _visualTrimEnd = newStart + length;
          break;

        case TrimBoundaries.none:
          break;
      }
    });
  }

  // Overlay methods removed - handled by parent GestureDetector in video_time_line.dart

  @override
  void dispose() {
    _thumbnailNotifier.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    // Calculate track width using the same approach as provider (use totalDuration from model)
    // This ensures consistency with timeline container calculations
    final width = MediaQuery.of(context).size.width;
    final trackWidth = (width / 8) * widget.videoTrack.totalDuration;

    return Container(
      width: trackWidth,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Main track container (no absolute positioning)
          Container(
            width: trackWidth,
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(
                color: widget.selectedTrackBorderColor,
                width: widget.isSelected ? 2 : 0,
              ),
            ),
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onLongPressStart: (details) {
                // Provide haptic feedback for better UX
                HapticFeedback.mediumImpact();
                // Call the callback if provided
                widget.onLongPress?.call(details.globalPosition);
              },
              onHorizontalDragStart: (_) {
                _boundary = TrimBoundaries.inside;
                _onPanStart();
              },
              onHorizontalDragUpdate: (details) {
                _onPanUpdate(details, trackWidth);
              },
              onHorizontalDragEnd: (_) {
                _onPanEnd();
              },
              child: ValueListenableBuilder<List<File>>(
                valueListenable: _thumbnailNotifier,
                builder: (context, thumbnails, _) {
                  // Calculate visible thumbnail range based on trim values for normal display
                  List<File> visibleThumbnails = [];
                  List<File> displayThumbnails =
                      []; // What to actually show (switches based on dragging state)

                  if (thumbnails.isNotEmpty &&
                      widget.videoTrack.originalDuration > 0) {
                    final originalDuration = widget.videoTrack.originalDuration;
                    final trimStart = widget.videoTrack.videoTrimStart;
                    final trimEnd = widget.videoTrack.videoTrimEnd;

                    // Handle edge cases for filtered thumbnails
                    if (trimStart >= trimEnd) {
                      // Invalid trim range - show empty
                      visibleThumbnails = [];
                    } else if (trimStart <= 0 && trimEnd >= originalDuration) {
                      // No trim applied - show all thumbnails
                      visibleThumbnails = thumbnails;
                    } else {
                      // Calculate which thumbnails correspond to the trimmed duration
                      final startIndex =
                          ((trimStart / originalDuration) * thumbnails.length)
                              .floor()
                              .clamp(0, thumbnails.length);
                      final endIndex =
                          ((trimEnd / originalDuration) * thumbnails.length)
                              .ceil()
                              .clamp(0, thumbnails.length);

                      // Ensure we have at least one thumbnail if trim range is valid
                      final safeStartIndex =
                          startIndex.clamp(0, thumbnails.length - 1);
                      final safeEndIndex = (endIndex <= safeStartIndex)
                          ? safeStartIndex + 1
                          : endIndex.clamp(
                              safeStartIndex + 1, thumbnails.length);

                      print("=== Thumbnail filtering ===");
                      print("Original duration: $originalDuration");
                      print("Trim: $trimStart - $trimEnd");
                      print("Total thumbnails: ${thumbnails.length}");
                      print("Calculated indices: $startIndex to $endIndex");
                      print("Safe indices: $safeStartIndex to $safeEndIndex");

                      visibleThumbnails =
                          thumbnails.sublist(safeStartIndex, safeEndIndex);
                    }

                    // Choose which thumbnails to display based on dragging state
                    if (_isDragging) {
                      // During dragging: show current state thumbnails (what's currently visible) for context
                      displayThumbnails = visibleThumbnails.isNotEmpty
                          ? visibleThumbnails
                          : thumbnails;
                      print(
                          "=== Dragging mode: showing current state thumbnails ===");
                      print(
                          "Display thumbnails count: ${displayThumbnails.length}");
                    } else {
                      // Normal state: show filtered thumbnails
                      displayThumbnails = visibleThumbnails;
                    }
                  }

                  return Stack(
                    children: [
                      // Show loading indicator or thumbnails
                      _isGeneratingThumbnails
                          ? Container(
                              height: 60, // Match thumbnail height
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.7),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SizedBox(
                                        width: 14,
                                        height: 14,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                            Colors.white.withValues(alpha: 0.9),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 6),
                                      Text(
                                        'Loading...',
                                        style: TextStyle(
                                          color: Colors.white
                                              .withValues(alpha: 0.9),
                                          fontSize: 11,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : Container(
                              height: 60,
                              child: displayThumbnails.isNotEmpty
                                  ? ListView.builder(
                                      shrinkWrap: true,
                                      padding: EdgeInsets.zero,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      scrollDirection: Axis.horizontal,
                                      itemCount: displayThumbnails.length,
                                      itemBuilder: (context, index) {
                                        return SizedBox(
                                          width: trackWidth /
                                              displayThumbnails.length,
                                          child: Image.file(
                                            displayThumbnails[index],
                                            fit: BoxFit.cover,
                                          ),
                                        );
                                      },
                                    )
                                  : const Center(
                                      child: Icon(Icons.broken_image_outlined)),
                            ),

                      // Trim overlay for visual feedback during dragging only
                      if (_isDragging)
                        CustomPaint(
                          size: Size(trackWidth, 60),
                          painter: TrimOverlayPainter(
                            visualTrimStart: _visualTrimStart,
                            visualTrimEnd: _visualTrimEnd,
                            currentTrimStart: widget.videoTrack.videoTrimStart,
                            currentTrimEnd: widget.videoTrack.videoTrimEnd,
                            originalDuration:
                                widget.videoTrack.originalDuration,
                            isDragging: _isDragging,
                            thumbnailCount: displayThumbnails.length,
                          ),
                        ),
                      // Duration label - positioned in center to avoid handle overlap
                      Positioned(
                        bottom: 2,
                        left: trackWidth / 2 - 15, // Center horizontally
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.8),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            "${widget.videoTrack.totalDuration}s",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),

          // Start handle - positioned at track start (left boundary)
          if (widget.isSelected)
            Positioned(
              left:
                  0, // Always at track start since track width represents trimmed content
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onHorizontalDragStart: (_) {
                  _boundary = TrimBoundaries.start;
                  _onPanStart();
                },
                onHorizontalDragUpdate: (details) {
                  _onPanUpdate(details, trackWidth);
                },
                onHorizontalDragEnd: (_) {
                  _onPanEnd();
                },
                child: Container(
                  width: 20,
                  height: 60,
                  decoration: BoxDecoration(
                    color: widget.selectedTrackBorderColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(4),
                      bottomLeft: Radius.circular(4),
                    ),
                  ),
                  child: Center(
                    child: Container(
                      width: 2,
                      height: 15,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(
                          Radius.circular(double.maxFinite),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

          // End handle - positioned at track end (right boundary)
          if (widget.isSelected)
            Positioned(
              right:
                  0, // Always at track end since track width represents trimmed content
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onHorizontalDragStart: (_) {
                  _boundary = TrimBoundaries.end;
                  _onPanStart();
                },
                onHorizontalDragUpdate: (details) {
                  _onPanUpdate(details, trackWidth);
                },
                onHorizontalDragEnd: (_) {
                  _onPanEnd();
                },
                child: Container(
                  width: 20,
                  height: 60,
                  decoration: BoxDecoration(
                    color: widget.selectedTrackBorderColor,
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(4),
                      bottomRight: Radius.circular(4),
                    ),
                  ),
                  child: Center(
                    child: Container(
                      width: 2,
                      height: 15,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.all(
                          Radius.circular(double.maxFinite),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
