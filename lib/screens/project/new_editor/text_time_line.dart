import 'package:ai_video_creator_editor/screens/project/new_editor/text_track.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class TextTimeline extends StatefulWidget {
  const TextTimeline({
    super.key,
    this.previewHeight,
  });

  final double? previewHeight;

  @override
  State<TextTimeline> createState() => _TextTimelineState();
}

class _TextTimelineState extends State<TextTimeline>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    final width = MediaQuery.of(context).size.width;
    return Consumer<VideoEditorProvider>(
      builder: (context, provider, child) {
        final timelineDuration = provider.videoDuration;
        final timelineWidth = timelineDuration * (width / 8);
        return Container(
          margin: EdgeInsets.only(right: 1.0), // Match video timeline margin
          width: timelineWidth,
          child: Stack(
            children: provider.textTracks.map((textTrack) {
              final index = provider.textTracks.indexOf(textTrack);
              return TextTrack(
                key: ValueKey(
                    '${textTrack.id}_${textTrack.lastModified.millisecondsSinceEpoch}'),
                index: index,
                textTrack: provider.textTracks[index],
                isSelected: provider.selectedTextTrackIndex == index,
                timelineWidth: timelineWidth,
                timelineDuration: timelineDuration,
                selectedTrackBorderColor: provider.selectedTrackBorderColor,
                previewHeight: widget.previewHeight,
              );
            }).toList(),
          ),
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
