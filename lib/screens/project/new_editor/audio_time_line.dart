import 'package:ai_video_creator_editor/screens/project/new_editor/audio_track.dart';
import 'package:ai_video_creator_editor/screens/project/new_editor/video_editor_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AudioTimeline extends StatefulWidget {
  const AudioTimeline({super.key});

  @override
  State<AudioTimeline> createState() => _AudioTimelineState();
}

class _AudioTimelineState extends State<AudioTimeline>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    final width = MediaQuery.of(context).size.width;
    return Consumer<VideoEditorProvider>(
      builder: (context, provider, child) {
        final timelineDuration = provider.videoDuration;
        final timelineWidth = timelineDuration * (width / 8);
        // Ensure audio controllers are created and master timeline is updated
        // This happens automatically when audio tracks are added/removed through the provider
        
        return Container(
          margin: EdgeInsets.only(right: 1.0), // Match video timeline margin
          width: timelineWidth,
          child: Stack(
            children: provider.audioTracks.map((audioTrack) {
              final index = provider.audioTracks.indexOf(audioTrack);
              return AudioTrack(
                key: ValueKey(
                    '${audioTrack.id}_${audioTrack.lastModified.millisecondsSinceEpoch}'),
                index: index,
                audioTrack: audioTrack,
                isSelected: provider.selectedAudioTrackIndex == index,
                timelineWidth: timelineWidth,
                timelineDuration: timelineDuration,
                selectedTrackBorderColor: provider.selectedTrackBorderColor,
              );
            }).toList(),
          ),
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}
